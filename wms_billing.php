<?php
if(isset($_GET['dynamicDataProvider'])) {
    $_GET['donotshowheader'] = 1;
  }
  
include('header.php');
include('session.php');

//get account details
$clientQuery = doQuery("SELECT * FROM mf_accounts mf
                        LEFT JOIN web_mf_accounts wmf ON mf.account = wmf.account AND mf.hubcode = wmf.hubcode");
$clientInfo = mysql_fetch_array($clientQuery);
$clients = [];
while ($client = mysql_fetch_array($clientQuery)) {
    $clients[] = $client;
}


$activeClient = LWLI_CLIENT_ONLINE;

//get invoice number
$counter_check = 0;
$isseriesnumvalid = false;
while(!$isseriesnumvalid) {
	$query=doQuery("select series_from,series_to,bill_soa_series_magic FROM web_bill_soa_series WHERE isused=1 ".$departments_filter." order by series_from limit 1");
	$infoNB = mysql_fetch_array($query);
	if(mysql_num_rows($query)==0) {
		echo json_encode(array('error'=>true,'message'=> 'No available invoice series.'));
		exit;
	}
	if($infoNB['series_to'] >= $infoNB['series_from']) {
		for($x=$infoNB['series_from']; $x<=$infoNB['series_to']; $x++) {
			echo $x;
		  $query=doQuery("select billvt_refnum FROM web_bill_accounts WHERE trim(billvt_refnum)='".trim($x)."'");
		  if(mysql_num_rows($query) == 0) {
			 $lastVTRefNum = $x;
			 $isseriesnumvalid = true;
			 break;
		  }
		} 
	}
	
   	$counter_check++;
	
	if($counter_check==10) break;
 }


 $qWmsServices = doQuery("SELECT * FROM web_wms_client_services_billing wwcsb
                         LEFT JOIN web_wms_client_services wwcs 
                         ON wwcs.service_id = wwcsb.service_id
                         WHERE account = '" . LWLI_CLIENT_ONLINE . "' ORDER BY service_name");


?>

<body>
    <?php include('navbar_menu.php'); ?>
	<div id="headerfixedmenuspacer">&nbsp;</div>

    <div class="container">
    <ul class="nav nav-tabs">
        <li class="active"><a data-toggle="tab" href="#create-billing">Create Billing</a></li>
        <li><a data-toggle="tab" href="#display-billing">Billing Display</a></li>
    </ul>
    </br>
    <div class="tab-content">
        <!-- Create Billing Tab -->
        <div id="create-billing" class="tab-pane fade in active">
        <div class="panel panel-default alert-billing-lwli">
		  	<div class="panel-body">
		  		<span style="height: 8px; line-height: 8px; float: left; font-size:18px; font-weight:bold;">BILLING (Statement of Account)</span>
		  		<span style="height: 9px; line-height: 9px; float: right; font-size:18px;font-weight:bold;">Invoice Number: <?= $lastVTRefNum ?> </span>
		  	</div>
		</div>
        <div class="panel panel-default">
		  	<div class="panel-body">
		  		<div class="row">
		  			<div class="col-sm-6">
		  				<p>Client Name: <?php echo $clientInfo['name']  ?></p>
		  				<p>Client Code: <?php echo $_SESSION['AccountCode'] ?></p>
		  				<p>Address: <?php echo $clientInfo['street'].', '.$clientInfo['brgymun'].', '.$clientInfo['cityprov'].', '.$clientInfo['zipcode'] ?></p>
		  			</div>
		  			<div class="col-sm-6" style="text-align: right;">
		  				<p>TIN:/SC TIN: <?php echo $clientInfo['tin_no'] ?></p>
		  				<p>Business Style: <?php echo $clientInfo['business_style'] ?></p>
		  			</div>
		  		</div>
		  	</div>
		</div>

            <!--<div class="form-group">
                <label for="client_id">Client</label>
                <select class="form-control" name="client_id" id="client_id" required>
                    <option value="">Select Client</option>
                    <?php foreach ($clients as $client): ?>
                        <option value="<?php echo $client['account']; ?>" <?php if ($client['account'] == $activeClient) echo 'selected'; ?>><?= htmlspecialchars($client['name']) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>-->
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="bill_date">Bill Date</label>
                                <div class='input-group date' id='billdate'>
                                    <input type='text' class="form-control" id="bill_date" value="<?php echo date('Y-m-d'); ?>"/>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                            <script type="text/javascript">
                                $(function () {
                                    $('#billdate').datetimepicker({
                                        format: 'YYYY-MM-DD',
                                        maxDate: 'now'
                                    });
                                });
                            </script>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="end_date">Billing Cutoff Period</label>
                                <div class='input-group date' id='cutoff'>
                                    <input type='text' class="form-control" id="cutoff_date" value="<?php echo date('Y-m-t', strtotime('last day of previous month')); ?>"/>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                            <script type="text/javascript">
                                $(function () {
                                    $('#cutoff').datetimepicker({
                                        format: 'YYYY-MM-DD',
                                    });
                                });
                            </script>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label for="end_date">Service Charges</label>
                                <select class="form-control" id="serviceCharges" multiple data-live-search="true" data-selected-text-format="count">
                                    <option value="">ALL</option>
                                    <?php while ($rows_qWmsServices = mysql_fetch_array($qWmsServices)) { ?>
                                    <option value="<?php echo $rows_qWmsServices['service_id']; ?>"><?php echo $rows_qWmsServices['service_name'].' - '.$rows_qWmsServices['unit_description']; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button class="btn btn-primary btn-block" id="fetchServices">Fetch Services</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table to display service data -->
            <div id="serviceTableContainer" style="margin-top: 20px; display: none;">
                <h4>Service Details</h4>
                <table id="serviceTable" class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="text-align: center;">Service Name</th>
                            <th style="text-align: center;">Unit Description</th>
                            <th style="text-align: center;">Minimum Qty</th>
                            <th style="text-align: center;">Billable Qty</th>
                            <th style="text-align: center;">Rate</th>
                            <th style="text-align: center;">Total Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Rows will be dynamically added here -->
                    </tbody>
                </table>

                 <!-- Add the "Create Billing" button below the table -->
                 <button class="btn btn-success pull-right" style="margin: 0 auto;" id="createBillingButton" style="margin-top: 10px;">Create Billing</button>
                 <input type="hidden" id="edit_bill_magic" value="" />

            </div>
        </div>

        <!-- Billing Display Tab -->
        <div id="display-billing" class="tab-pane fade">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 style="margin-top: 5px; margin-bottom: 5px;"><i class="glyphicon glyphicon-list-alt"></i> Billing History</h4>
                </div>
                <div class="panel-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="pull-right">
                                <div class="form-inline">
                                    <div class="form-group">
                                        <input type="text" id="billing-search" class="form-control" placeholder="Search billings...">
                                    </div>
                                    <div class="form-group ml-2">
                                        <select class="form-control" id="status-filter">
                                            <option value="">All Statuses</option>
                                            <option value="Approved">Approved</option>
                                            <option value="For Approval">For Approval</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="billingTable">
                            <thead>
                                <tr>
                                    <th>Client Name</th>
                                    <th>Billing Date</th>
                                    <th>Invoice Number</th>
                                    <th>Created By</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th style="width: 120px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                    $qdata="SELECT bill_magic, account, bill_date, billvt_refnum, encodedby, totalamount, isapproved FROM web_bill_accounts WHERE account='".$activeClient."' AND bill_type=2 ";
                                    $qbillingaccount=doQuery($qdata);
                                    while($result=mysql_fetch_array($qbillingaccount)){
                                        $quser="SELECT * FROM web_webusers WHERE user='".$result['encodedby']."'";
                                        $quser=doQuery($quser);
                                        $user=mysql_fetch_array($quser);
                                        $createdby=$user['firstname'].' '.$user['lastname'];
                                        
                                        // For demo purposes - you may want to replace with actual status from database
                                        $statusClass = $result['isapproved'] == 1 ? 'success' : 'warning';
                                 ?>
                                    <tr>
                                        <td><?= $result['account'] ?></td>
                                        <td><?= date('M d, Y', strtotime($result['bill_date'])) ?></td>
                                        <td><span class="label label-info"><?= $result['billvt_refnum'] ?></span></td>
                                        <td><?= $createdby ?></td>
                                        <td class="text-right"><?= number_format($result['totalamount'], 2) ?></td>
                                        <td><span class="label label-<?= $statusClass ?>">
                                            <?php if ($result['isapproved'] == 1) { echo 'Approved'; } else { echo 'For Approval'; } ?>
                                        </span></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="print_wms_billing.php?billmagic=<?= $result['bill_magic'] ?>" target="_blank" class="btn btn-default" title="Print">
                                                    <i class="glyphicon glyphicon-print"></i>
                                                </a>
                                                <button class="btn btn-info btn-attachment-billing" data-id="<?= $result['bill_magic'] ?>" title="Attachments">
                                                    <i class="glyphicon glyphicon-paperclip"></i>
                                                </button>
                                                <button class="btn btn-warning btn-edit-billing" data-id="<?= $result['bill_magic'] ?>" title="Edit" <?= ($result['isapproved'] == 1 ? 'disabled' : '') ?>>
                                                    <i class="glyphicon glyphicon-edit"></i>
                                                </button>
                                                <button class="btn btn-danger btn-cancel-billing" data-id="<?= $result['bill_magic'] ?>" title="Cancel" <?= ($result['isapproved'] == 1 ? 'disabled' : '') ?>><i class="glyphicon glyphicon-ban-circle"></i></button>
                                                <button class="btn btn-success btn-approve-billing" data-id="<?= $result['bill_magic'] ?>" title="Approve" <?= ($result['isapproved'] == 1 ? 'disabled' : '') ?>>
                                                    <i class="glyphicon glyphicon-ok"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        // Initialize DataTables with improved configuration
        var billingTable = $('#billingTable').DataTable({
            responsive: true,
            dom: '<"top"lf>rt<"bottom"ip>',
            language: {
                search: "",
                searchPlaceholder: "Search records...",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ billings"
            },
            order: [[1, 'desc']], // Order by billing date descending
            pageLength: 10,
            lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            columnDefs: [
                { className: "text-center", targets: [6] } // Center align the actions column
            ]
        });
        
        // Hide the default search box as we'll use our custom one
        $('.dataTables_filter').hide();
        
        // Connect our custom search box to DataTables
        $('#billing-search').on('keyup', function() {
            billingTable.search(this.value).draw();
        });
        
        // Connect status filter to DataTables
        $('#status-filter').on('change', function() {
            var val = this.value;
            if (val === "") {
                billingTable.column(5).search('').draw();
            } else {
                billingTable.column(5).search(val, true, false).draw();
            }
        });
        
        $('#serviceCharges').selectpicker();
        var billingData = [];

        // Function to reset the Create Billing form
        function resetCreateBillingForm() {
            $('#bill_date').val('<?php echo date('Y-m-d'); ?>');
            $('#cutoff_date').val('<?php echo date('Y-m-t', strtotime('last day of previous month')); ?>');
            $('#serviceCharges').val(''); // Clear selected services
            $('#serviceCharges').selectpicker('render');
            $('#serviceCharges').selectpicker('refresh');
            $('#serviceTable tbody').empty();
            $('#serviceTableContainer').hide();
            $('#edit_bill_magic').val(''); // Clear the edit_bill_magic
            $('.alert-billing-lwli span:first-child').text('BILLING (Statement of Account)'); // Reset title
            $('.alert-billing-lwli span:last-child').html('Invoice Number: <?= $lastVTRefNum ?>'); // Reset invoice num display
        }

        // Listen for tab changes to reset the form if switching away from Create Billing
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            if ($(e.target).attr('href') === '#create-billing') {
                // Optionally, reset only if it was a new creation intention
            } else if ($(e.relatedTarget).attr('href') === '#create-billing') {
                // If coming from create-billing, we might want to clear it.
                resetCreateBillingForm();
            }
        });

        function createBillingButton() {
            var clientId = '<?php echo $activeClient ?>';
            var bill_date = $('#bill_date').val();
            var cutoff_date = $('#cutoff_date').val();
            var created_by = '<?= $_SESSION['userid'] ?>';
            var invoice_num = $('.alert-billing-lwli span:last-child').text().replace('Invoice Number: ', '').trim(); // Get from displayed element
            var tin_no = '<?= $clientInfo['tin_no'] ?>';
            var editBillMagic = $('#edit_bill_magic').val(); // Get the bill_magic if in edit mode

            if (!clientId || !bill_date || !cutoff_date) {
                alert('Please fill in all fields.');
                return;
            }

            if (!Array.isArray(billingData) || billingData.length === 0) {
                alert('No billing data found. Please fetch services before saving.');
                return;
            }

            var totalAmount = billingData.reduce((sum, service) => {
                return sum + parseFloat(service.billAmount || 0);
            }, 0);

            var billingSummary = {
                client_id: clientId,
                bill_date: bill_date,
                cutoff_date: cutoff_date,
                created_by: created_by,
                invoice_num: invoice_num,
                tin_no: tin_no,
                totalAmount: totalAmount.toFixed(2),
                services: billingData
            };

            // Add bill_magic to the summary if in edit mode
            if (editBillMagic) {
                billingSummary.bill_magic = editBillMagic;
            }

            console.log(billingSummary);
            $.ajax({
                url: "/lwli/index.php/WMSBilling/saveBilling",
                method: 'POST',
                data: JSON.stringify(billingSummary),
                contentType: 'application/json',
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.success) {
                        alert('Billing saved successfully!');
                        window.location.reload();
                    } else {
                        alert('Failed to save billing: ' + data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error saving billing:', error);
                    alert('An error occurred. Please try again.');
                }
            });
        }

        function fetchServices() {
            var clientId = '<?php echo $activeClient ?>';
            var bill_date = $('#bill_date').val();
            var cutoff_date = $('#cutoff_date').val();
            var service_filter = $('#serviceCharges').val();
            var editBillMagic = $('#edit_bill_magic').val(); // Get the bill_magic if in edit mode

            if (!clientId || !bill_date || !cutoff_date) {
                alert('Please fill in all fields.');
                return;
            }

            var requestData = {
                client_id: clientId,
                bill_date: bill_date,
                cutoff_date: cutoff_date,
                service_filter: service_filter
            };

            // Add bill_magic to request data if in edit mode
            if (editBillMagic) {
                requestData.bill_magic = editBillMagic;
                console.log('Edit mode - bill_magic:', editBillMagic);
            }

            $.ajax({
                url: "/lwli/index.php/WMSBilling/createBilling",
                method: 'POST',
                data: requestData,
                success: function(response) {
                    var data = JSON.parse(response);

                    $('#serviceTable tbody').empty();

                    if (data.serviceData) {
                        billingData = Object.values(data.serviceData); 
                        console.log(billingData);
                        var totalBill = 0;

                        billingData.forEach(function(service) {
                            if (parseFloat(service.billAmount) > 0) {
                                var row = `<tr>
                                    <td>${service.service_name}</td>
                                    <td>${service.unit_description}</td>
                                    <td>${service.minimum_qty || 'N/A'}</td>
                                    <td style="text-align: right;">${parseFloat(service.billable_count).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                                    <td style="text-align: right;">${parseFloat(service.rate).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                                    <td style="text-align: right;">${parseFloat(service.billAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                                </tr>`;
                                $('#serviceTable tbody').append(row);

                                totalBill += parseFloat(service.billAmount);
                            }
                        });

                        var formattedTotalBill = totalBill.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });

                        // Add a row for the total bill
                        var totalRow = `<tr style="font-weight: bold;">
                            <td colspan="5" style="text-align: right;">Total Billed Amount:</td>
                            <td style="text-align: right;">${formattedTotalBill}</td>
                        </tr>`;
                        $('#serviceTable tbody').append(totalRow);

                        $('#serviceTableContainer').show();

                        // Populate Bill Date
                        $('#bill_date').val(data.billing_info.bill_date);

                        // Populate Invoice Number and TIN
                        $('.alert-billing-lwli span:last-child').text('Invoice Number: ' + data.billing_info.billvt_refnum);
                        // tin_no is displayed directly in HTML, no input for it. If it was an input, we'd set it here.

                        // Store bill_magic for update operations (only if not already in edit mode)
                        if (!$('#edit_bill_magic').val()) {
                            $('#edit_bill_magic').val(data.billing_info.bill_magic);
                        }

                        // Update tab title to indicate editing (only if we have edit_bill_magic)
                        if ($('#edit_bill_magic').val()) {
                            $('.alert-billing-lwli span:first-child').text('EDIT BILLING (Statement of Account)');
                        }

                        // Select services in the dropdown
                        var selectedServices = [];
                        data.services.forEach(function(service) {
                            if (service.service_id) {
                                selectedServices.push(service.service_id);
                            }
                        });
                        $('#serviceCharges').val(selectedServices);
                        $('#serviceCharges').selectpicker('render');
                        $('#serviceCharges').selectpicker('refresh');

                        // Trigger fetchServices to display the data in the table
                        fetchServices();

                    } else {
                        alert('No service data found.');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching data:', error);
                    alert('An error occurred. Please try again.');
                }
            });
        }

        // Attach the function to the button click event
        $('#fetchServices').on('click', fetchServices);
        $('#createBillingButton').on('click', createBillingButton);

        // Handle Edit button click
        $(document).on('click', '.btn-edit-billing', function() {
            var billMagic = $(this).data('id');
            
            // Fetch billing details from the backend
            $.ajax({
                url: "/lwli/index.php/WMSBilling/getBillingDetails/" + billMagic,
                method: 'GET',
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.billing_info) {
                        // Switch to Create Billing tab
                        $('a[href="#create-billing"]').tab('show');

                        // Store the original bill_magic for editing
                        $('#edit_bill_magic').val(billMagic);

                        // Populate Bill Date
                        $('#bill_date').val(data.billing_info.bill_date);

                        // Populate Cutoff Date
                        $('#cutoff_date').val(data.billing_info.cutoff_period);

                        // Populate Invoice Number
                        $('.alert-billing-lwli span:last-child').text('Invoice Number: ' + data.billing_info.billvt_refnum);

                        // Update tab title to indicate editing
                        $('.alert-billing-lwli span:first-child').text('EDIT BILLING (Statement of Account)');

                        // Select services in the dropdown
                        var selectedServices = [];
                        data.services.forEach(function(service) {
                            if (service.service_id) {
                                selectedServices.push(service.service_id);
                            }
                        });
                        $('#serviceCharges').val(selectedServices);
                        $('#serviceCharges').selectpicker('render');
                        $('#serviceCharges').selectpicker('refresh');

                        // Trigger fetchServices to display the data in the table
                        fetchServices();

                    } else {
                        alert('Failed to load billing details: ' + data.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching billing details:', error);
                    alert('An error occurred while fetching billing details. Please try again.');
                }
            });
        });

        // Handle Attachment button click
        $(document).on('click', '.btn-attachment-billing', function() {
            var billMagic = $(this).data('id');
            
            // Fetch billing attachments from the backend
            $.ajax({
                url: "/lwli/index.php/WMSBilling/getBillingAttachments/" + billMagic,
                method: 'GET',
                success: function(response) {
                    var data = JSON.parse(response);
                    if (data.billing_info) {
                        showAttachmentsModal(data);
                    } else {
                        alert('Failed to load billing attachments: ' + data.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching billing attachments:', error);
                    alert('An error occurred while fetching billing attachments. Please try again.');
                }
            });
        });

        function showAttachmentsModal(data) {
            var modalHtml = `
                <div class="modal fade" id="attachmentsModal" tabindex="-1" role="dialog" aria-labelledby="attachmentsModalLabel">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <h4 class="modal-title" id="attachmentsModalLabel">
                                    Billing Attachments - Invoice #${data.billing_info.billvt_refnum}
                                </h4>
                            </div>
                            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                                <div class="row">
                                    <div class="col-md-12">
                                        <p><strong>Client:</strong> ${data.billing_info.account}</p>
                                        <p><strong>Billing Date:</strong> ${data.billing_info.bill_date}</p>
                                        <p><strong>Cutoff Period:</strong> ${data.billing_info.cutoff_period}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <ul class="nav nav-tabs" id="attachmentTabs">
                                            ${Object.keys(data.attachments).map(function(serviceName, index) {
                                                return `<li class="${index === 0 ? 'active' : ''}">
                                                    <a href="#tab-${index}" data-toggle="tab">${serviceName}</a>
                                                </li>`;
                                            }).join('')}
                                        </ul>
                                        <div class="tab-content" id="attachmentTabContent" style="margin-top: 15px;">
                                            ${Object.keys(data.attachments).map(function(serviceName, index) {
                                                var service = data.attachments[serviceName];
                                                var sourceData = service.source_data;
                                                if (serviceName === 'Storage' && sourceData && sourceData.days) {
                                                    // Render transposed storage table
                                                    let html = '<div class="tab-pane fade ' + (index === 0 ? 'in active' : '') + '" id="tab-' + index + '">';
                                                    html += '<div class="panel panel-default">';
                                                    html += '<div class="panel-heading">';
                                                    html += '<h5 class="panel-title">' + serviceName + '</h5>';
                                                    html += '<p><strong>Billable Qty:</strong> ' + service.service_info.billable_qty + ' | ' +
                                                        '<strong>Rate:</strong> ' + service.service_info.rate + ' | ' +
                                                        '<strong>Total Amount:</strong> ' + service.service_info.bill_amount + '</p>';
                                                    html += '<button class="btn btn-sm btn-default print-table-btn" data-tab="tab-' + index + '">Print</button>';
                                                    html += '</div>';
                                                    html += '<div class="panel-body"><div class="table-responsive">';
                                                    html += '<table class="table table-striped table-bordered attachment-table" id="attachment-table-' + index + '">';
                                                    // Header row: dates
                                                    html += '<thead><tr><th>BIN Detail</th>';
                                                    sourceData.days.forEach(function(date) { html += '<th>' + date + '</th>'; });
                                                    html += '</tr></thead><tbody>';
                                                    // Allocation row
                                                    html += '<tr><td>Allocation</td>' + sourceData.days.map(function(date) { return '<td>' + sourceData.allocation + '</td>'; }).join('') + '</tr>';
                                                    // Usage row
                                                    html += '<tr><td>Usage</td>' + sourceData.days.map(function(date) { return '<td>' + sourceData.usage[date] + '</td>'; }).join('') + '</tr>';
                                                    // EXCESS USAGE row
                                                    html += '<tr><td>EXCESS USAGE</td>' + sourceData.days.map(function(date) { return '<td>' + sourceData.excess[date] + '</td>'; }).join('') + '</tr>';
                                                    // RATE row
                                                    html += '<tr><td>RATE</td>' + sourceData.days.map(function(date) { return '<td>' + Number(sourceData.rate).toFixed(2) + '</td>'; }).join('') + '</tr>';
                                                    // Amount Due row
                                                    html += '<tr><td>Amount Due</td>' + sourceData.days.map(function(date) { return '<td>' + Number(sourceData.amount_due[date]).toFixed(2) + '</td>'; }).join('') + '</tr>';
                                                    html += '</tbody><tfoot>';
                                                    html += '<tr><td>TOTAL</td><td colspan="' + sourceData.days.length + '">' + Number(sourceData.total).toFixed(2) + '</td></tr>';
                                                    html += '<tr><td>VAT</td><td colspan="' + sourceData.days.length + '">' + Number(sourceData.vat).toFixed(2) + '</td></tr>';
                                                    html += '<tr><td>TOTAL AMOUNT</td><td colspan="' + sourceData.days.length + '"><b>' + Number(sourceData.grand_total).toFixed(2) + '</b></td></tr>';
                                                    html += '</tfoot></table></div></div></div></div>';
                                                    return html;
                                                } else {
                                                    // Default rendering for other services
                                                    var tableHeaders = Object.keys(sourceData[0] || {}).map(function(key) {
                                                        return `<th>${key.replace(/_/g, ' ').toUpperCase()}</th>`;
                                                    }).join('');
                                                    var tableRows = sourceData.map(function(row) {
                                                        return `<tr>${Object.values(row).map(function(value) {
                                                            return `<td>${value || ''}</td>`;
                                                        }).join('')}</tr>`;
                                                    }).join('');
                                                    return `<div class="tab-pane fade ${index === 0 ? 'in active' : ''}" id="tab-${index}">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h5 class="panel-title">${serviceName}</h5>
                                                                <p><strong>Billable Qty:</strong> ${service.service_info.billable_qty} | 
                                                                   <strong>Rate:</strong> ${service.service_info.rate} | 
                                                                   <strong>Total Amount:</strong> ${service.service_info.bill_amount}</p>
                                                                <button class="btn btn-sm btn-default print-table-btn" data-tab="tab-${index}">Print</button>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="table-responsive">
                                                                    <table class="table table-striped table-bordered attachment-table" id="attachment-table-${index}">
                                                                        <thead>
                                                                            <tr>${tableHeaders}</tr>
                                                                        </thead>
                                                                        <tbody>${tableRows}</tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`;
                                                }
                                            }).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing modal if any
            $('#attachmentsModal').remove();
            
            // Add modal to body
            $('body').append(modalHtml);
            
            // Show modal
            $('#attachmentsModal').modal('show');

            // Print table functionality
            $('.print-table-btn').off('click').on('click', function() {
                var tabId = $(this).data('tab');
                var table = $("#" + tabId + " .attachment-table");
                if (table.length === 0) table = $("#attachment-table-" + tabId.split('-')[1]);
                var printWindow = window.open('', '', 'height=600,width=900');
                printWindow.document.write('<html><head><title>Print Table</title>');
                printWindow.document.write('<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">');
                printWindow.document.write('</head><body >');
                printWindow.document.write(table.parent().html());
                printWindow.document.write('</body></html>');
                printWindow.document.close();
                printWindow.focus();
                setTimeout(function() { printWindow.print(); printWindow.close(); }, 500);
            });
        }

        // Handle Cancel button click
        $(document).on('click', '.btn-cancel-billing', function() {
            var billMagic = $(this).data('id');
            if (confirm('Are you sure you want to cancel this billing?')) {
                $.ajax({
                    url: '/lwli/index.php/WMSBilling/cancelBilling/' + billMagic,
                    method: 'POST',
                    success: function(response) {
                        var data = JSON.parse(response);
                        if (data.success) {
                            alert('Billing cancelled successfully!');
                            window.location.reload();
                        } else {
                            alert('Failed to cancel billing: ' + data.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while cancelling billing.');
                    }
                });
            }
        });

        // Add JS for approve button
        $(document).on('click', '.btn-approve-billing', function() {
            var billMagic = $(this).data('id');
            if (confirm('Are you sure you want to approve this billing? Once approved, it cannot be edited or cancelled.')) {
                $.ajax({
                    url: '/lwli/index.php/WMSBilling/approveBilling/' + billMagic,
                    method: 'POST',
                    success: function(response) {
                        var data = JSON.parse(response);
                        if (data.success) {
                            alert('Billing approved successfully!');
                            window.location.reload();
                        } else {
                            alert('Failed to approve billing: ' + data.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while approving billing.');
                    }
                });
            }
        });
    });

    function ShowDataDetails(eurl,etitle) {
	 <?php
	 $shipmentidlist = '';
	 if(is_array($billshipmentrecords) && sizeof($billshipmentrecords) > 0) {
		 for($x = 0; $x < sizeof($billshipmentrecords); $x++) {
			 $shpid = $billshipmentrecords[$x]['shpid'];
			 if($x > 0 && $shpid<>'') {
			   $shipmentidlist  .= ','.$shpid;
			 } else {
			   $shipmentidlist  = $shpid;
			 }
		 }
	 }
	 $parameter = '';
	 if(isset($_REQUEST['billshipmentmagic']) && $_REQUEST['billshipmentmagic'] > 0) {
	    $parameter = '&billshipmentmagic='.$_REQUEST['billshipmentmagic'];
	 }

	 $_SESSION['orderwaybillno'] = '';
	 if(trim($shipmentidlist) != '') {
	    $_SESSION['orderwaybillno'] = $shipmentidlist;
	 }
	 ?>
	 if(eurl.indexOf('?') >= 0) {
	   eurl = eurl + '&';
	 } else {
	   eurl = eurl + '?';
	 }
	 var xpar = '<?php echo $parameter ?>';
	 var otd = '';
	 var ota = '';
	 if($('#other_service_charge_desc').length) {
	   otd = $('#other_service_charge_desc').val();
	 }
	 if($('#other_service_charge_amount').length) {
	   ota = $('#other_service_charge_amount').val();
	 }
  var nvtd = '';
	 var nvt = '';
	 if($('#nvt_charge_desc').length) {
	   nvtd = $('#nvt_charge_desc').val();
	 }
	 if($('#nvt_charge_amount').length) {
	   nvt = $('#nvt_charge_amount').val();
	 }
  
  var tvalidateamount = '';
  if(typeof $("#sbtotal") != 'undefined') {
      tvalidateamount = '&tvalidateamount='+$("#sbtotal").val();
  }
	 var options = {
        url: eurl+'cutoffdate='+$('#cutoffdate').val()+'&departments='+$('#departments').val()+'&hubcode='+$('#hubcode').val()+'&show='+$('#whichtoshow').val()+'&category='+$('#category').val()+'&billtoshipper='+$('#billtoshipper').val()+'&other_service_charge_desc='+otd+'&other_service_charge_amount='+ota+xpar+'&nvt_charge_desc='+nvtd+'&nvt_charge_amount='+nvt+'&filterpod=1'+tvalidateamount+'&contact_p='+$('#contact_p').val()+'&orderno_p='+$('#orderno_p').val()+'&pickupdate_p='+$('#pickupdate_p').val()+'&island_p='+$('#island_p').val(),
        title: etitle,
        size: eModal.size.xl }
     eModal.iframe(options);
	}
</script>
</body>
</html>

